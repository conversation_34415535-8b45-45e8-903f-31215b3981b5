---
type: "always_apply"
---

# User Instructions for Blender AI Agent

## Overview
This AI agent is specifically designed to operate Blender 3D software through programmatic control using Blender MCP and visual feedback through ScreenMonitorMCP. The agent follows a structured workflow that combines precise programmatic operations with visual validation to ensure high-quality 3D content creation.

## How to Interact with the Agent

### 1. Basic Communication Patterns

#### Starting a Session
- **Preparation Request**: "Analyze the current Blender scene and prepare for 3D modeling"
- **Project Setup**: "Set up a new scene for [specific project type]"
- **Context Analysis**: "Review the current workspace and provide recommendations"

#### Making Requests
- **Be Specific**: Clearly describe what you want to create or modify
- **Provide Context**: Mention the purpose, style, or constraints
- **Set Expectations**: Indicate quality level, timeline, or specific requirements

#### Example Requests
```
"Create a photorealistic donut with procedural materials"
"Model a championship trophy with metallic finish"
"Set up a 4-viewport workspace for character modeling"
"Analyze the current scene and suggest lighting improvements"
```

### 2. Workflow Understanding

#### Standard Operation Sequence
1. **Scene Analysis**: Agent analyzes current Blender state
2. **Planning**: Agent explains the approach and asks for confirmation
3. **Execution**: Agent performs Blender operations step-by-step
4. **Validation**: Agent captures and analyzes visual results
5. **Iteration**: Agent refines based on visual feedback
6. **Documentation**: Agent documents successful workflows

#### Visual Feedback Loop
- The agent will regularly capture screenshots to validate progress
- Visual analysis helps ensure operations are working as expected
- You'll see both the code execution and visual verification results
- The agent learns from visual feedback to improve future operations

### 3. Request Types and Examples

#### 3D Modeling Requests
- **Basic Shapes**: "Add a cube and modify it into a house shape"
- **Complex Models**: "Create a detailed car model with proper topology"
- **Organic Modeling**: "Model a tree with realistic branch structure"
- **Architectural**: "Design a modern building facade"

#### Material and Texturing
- **Basic Materials**: "Apply a red metallic material to the selected object"
- **Procedural Materials**: "Create a wood texture using shader nodes"
- **PBR Materials**: "Set up realistic glass material with proper reflections"
- **Material Analysis**: "Analyze the current material setup and suggest improvements"

#### Lighting and Rendering
- **Scene Lighting**: "Set up three-point lighting for product visualization"
- **Environment Setup**: "Create an outdoor environment with HDRI lighting"
- **Render Settings**: "Configure Cycles rendering for high-quality output"
- **Lighting Analysis**: "Analyze current lighting and suggest improvements"

#### Scene Management
- **Organization**: "Organize the scene with proper collections and naming"
- **Cleanup**: "Clean up the scene and optimize for performance"
- **Workspace Setup**: "Configure the workspace for animation work"
- **View Configuration**: "Set up quadview with optimal camera angles"

### 4. Advanced Interaction Patterns

#### Iterative Development
- **Feedback Loop**: "Create this, then show me the result for feedback"
- **Progressive Refinement**: "Start simple and add detail progressively"
- **Alternative Approaches**: "Try different methods and compare results"
- **Quality Validation**: "Ensure this meets professional standards"

#### Learning and Documentation
- **Workflow Documentation**: "Document this process for future reference"
- **Best Practices**: "Show me the best way to approach this task"
- **Troubleshooting**: "Help me understand why this isn't working"
- **Optimization**: "How can we make this workflow more efficient?"

### 5. Communication Best Practices

#### Clear Instructions
- Use specific terminology when possible
- Mention any constraints or requirements upfront
- Indicate the intended use or purpose of the 3D content
- Specify quality expectations (draft, production, photorealistic)

#### Effective Feedback
- Be specific about what you like or dislike
- Provide reference images or examples when helpful
- Ask questions if you don't understand the agent's approach
- Request explanations for complex operations

#### Collaboration Tips
- Trust the agent's expertise in 3D workflows
- Allow time for visual validation steps
- Provide feedback during iterative processes
- Ask for documentation of successful workflows

### 6. Understanding Agent Capabilities

#### What the Agent Excels At
- Programmatic 3D modeling and scene setup
- Material creation and shader node networks
- Lighting setup and optimization
- Visual validation and quality assurance
- Workflow documentation and optimization
- Error detection and recovery

#### What to Expect
- Step-by-step execution with explanations
- Visual verification of each major operation
- Professional-quality 3D content creation
- Detailed documentation of successful workflows
- Learning and adaptation from feedback
- Robust error handling and recovery

#### Limitations to Consider
- Complex artistic decisions may require human input
- Very specific stylistic requirements need clear guidance
- Performance limitations with extremely complex scenes
- Some operations may require multiple iterations
- Visual validation adds time but ensures quality

### 7. Troubleshooting and Support

#### Common Issues
- **Scene Complexity**: Agent will optimize for performance automatically
- **Visual Discrepancies**: Agent will iterate until results match expectations
- **Tool Conflicts**: Agent handles tool coordination and resource management
- **API Errors**: Agent implements robust error recovery mechanisms

#### Getting Help
- Ask for explanations of any operation or decision
- Request alternative approaches if current method isn't working
- Ask for documentation of successful workflows
- Request performance optimization suggestions

#### Best Results Tips
- Start with clear, specific requests
- Provide feedback during the process
- Allow the agent to complete validation steps
- Ask questions to understand the workflow
- Document successful patterns for future use

### 8. Advanced Features

#### Workflow Automation
- The agent can create reusable workflow templates
- Complex operations can be broken down into manageable steps
- Successful patterns are documented for future reference
- Custom procedures can be developed for specific needs

#### Quality Assurance
- Visual validation ensures operations work as expected
- Geometric accuracy is verified programmatically
- Material properties are validated visually
- Performance optimization is applied automatically

#### Learning and Adaptation
- The agent learns from successful workflows
- Visual feedback improves future operations
- Error patterns are documented and avoided
- Best practices are continuously refined

## Getting Started

1. **Initial Setup**: Ask the agent to analyze your current Blender scene
2. **Define Goals**: Clearly describe what you want to create or achieve
3. **Collaborate**: Work with the agent through the iterative process
4. **Learn**: Ask questions and request documentation of successful workflows
5. **Iterate**: Provide feedback and refine results as needed

Remember: This agent combines the precision of programmatic control with the validation of visual feedback to ensure high-quality 3D content creation. Trust the process, provide clear feedback, and don't hesitate to ask questions about any aspect of the workflow.

## 9. Specialized Use Cases

### Architectural Visualization
- **Building Design**: "Create a modern house with realistic materials and lighting"
- **Interior Design**: "Design a living room with furniture and proper lighting"
- **Urban Planning**: "Model a city block with buildings and infrastructure"
- **Landscape Architecture**: "Design a park with trees, paths, and water features"

### Product Design
- **Industrial Design**: "Create a sleek smartphone design with realistic materials"
- **Jewelry Design**: "Model a diamond ring with proper gem settings"
- **Automotive Design**: "Design a car exterior with accurate proportions"
- **Furniture Design**: "Create a modern chair with ergonomic considerations"

### Character and Creature Design
- **Human Characters**: "Model a realistic human character with proper anatomy"
- **Fantasy Creatures**: "Create a dragon with detailed scales and wings"
- **Cartoon Characters**: "Design a stylized character for animation"
- **Mechanical Characters**: "Create a robot with articulated joints"

### Environmental Design
- **Natural Environments**: "Create a forest scene with realistic vegetation"
- **Sci-Fi Environments**: "Design a futuristic cityscape"
- **Fantasy Worlds**: "Create a magical landscape with mystical elements"
- **Historical Recreations**: "Model an ancient temple with period-accurate details"

## 10. Performance and Optimization Guidelines

### Scene Complexity Management
- The agent automatically monitors scene complexity
- Large scenes are optimized for performance
- Level-of-detail systems are implemented when needed
- Memory usage is tracked and optimized
- Performance warnings are provided when necessary

### Quality vs. Speed Balance
- **Draft Mode**: Fast operations for quick iterations
- **Production Mode**: High-quality operations for final output
- **Preview Mode**: Balanced quality for real-time feedback
- **Optimization Mode**: Focus on performance improvements

### Hardware Considerations
- The agent adapts to available system resources
- Operations are scaled based on hardware capabilities
- Memory limitations are respected and managed
- GPU acceleration is utilized when available
- Performance monitoring provides real-time feedback

## 11. Learning and Development

### Skill Building
- Start with simple projects and gradually increase complexity
- Learn from the agent's explanations and documentation
- Ask for alternative approaches to understand different methods
- Request tutorials for specific techniques or workflows
- Build a personal library of successful patterns

### Best Practices Development
- Document successful workflows for future reference
- Learn industry-standard techniques and conventions
- Understand the reasoning behind the agent's decisions
- Develop an eye for quality and attention to detail
- Build expertise in specific areas of interest

### Community and Resources
- The agent can provide references to additional learning resources
- Industry best practices are incorporated into workflows
- Latest techniques and trends are integrated when appropriate
- Community standards and conventions are followed
- Professional workflows are documented and shared

## 12. Troubleshooting Guide

### Common Issues and Solutions

#### Visual Discrepancies
- **Issue**: Result doesn't match expectations
- **Solution**: Provide specific feedback about what needs to change
- **Prevention**: Use clear, detailed descriptions in initial requests

#### Performance Problems
- **Issue**: Operations are slow or system becomes unresponsive
- **Solution**: Agent will automatically optimize and suggest alternatives
- **Prevention**: Mention performance requirements upfront

#### Tool Conflicts
- **Issue**: Blender MCP or ScreenMonitorMCP not responding
- **Solution**: Agent implements automatic recovery and fallback methods
- **Prevention**: Regular system health checks are performed

#### Quality Issues
- **Issue**: Output quality is lower than expected
- **Solution**: Request specific quality improvements or alternative approaches
- **Prevention**: Specify quality requirements in initial request

### Getting the Best Results
1. **Be Specific**: Detailed requests lead to better outcomes
2. **Provide Context**: Explain the purpose and intended use
3. **Give Feedback**: Iterative improvement leads to better results
4. **Ask Questions**: Understanding the process improves collaboration
5. **Document Success**: Save successful workflows for future use

### When to Ask for Help
- When you don't understand a step in the process
- When results don't match your expectations
- When you need alternative approaches
- When you want to learn more about a technique
- When you encounter unexpected behavior

## 13. Advanced Collaboration Techniques

### Iterative Design Process
- **Concept Phase**: Start with basic shapes and forms
- **Development Phase**: Add detail and refinement
- **Validation Phase**: Test and verify against requirements
- **Optimization Phase**: Improve performance and quality
- **Documentation Phase**: Record successful workflows

### Feedback Integration
- Provide specific, actionable feedback
- Use visual references when helpful
- Explain preferences and requirements clearly
- Ask for explanations of technical decisions
- Request alternative approaches when needed

### Quality Assurance
- Review each step of the process
- Validate results against original requirements
- Test functionality and performance
- Ensure consistency across the project
- Document quality standards and preferences

## 14. Future Development and Updates

### Continuous Improvement
- The agent learns from each interaction
- Successful patterns are documented and reused
- Error patterns are identified and avoided
- Performance optimizations are continuously applied
- New techniques and best practices are integrated

### Feature Development
- New capabilities are added based on user needs
- Workflow optimizations are implemented regularly
- Tool integration is continuously improved
- Quality assurance processes are refined
- Documentation is updated with new insights

### Community Contribution
- Successful workflows are shared with the community
- Best practices are documented and distributed
- Innovation and experimentation are encouraged
- Knowledge sharing improves the overall system
- User feedback drives development priorities
